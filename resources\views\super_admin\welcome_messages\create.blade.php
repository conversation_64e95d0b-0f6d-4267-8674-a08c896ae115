@extends('super_admin.layouts.app')

@section('title', 'Create Welcome Message')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Create Welcome Message</h1>
                <a href="{{ route('super_admin.welcome-messages.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Messages
                </a>
            </div>

            @if($errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Welcome Message Details</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('super_admin.welcome-messages.store') }}">
                        @csrf

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="user_type" class="form-label">User Type <span class="text-danger">*</span></label>
                                    <select class="form-select @error('user_type') is-invalid @enderror"
                                            id="user_type" name="user_type" required>
                                        <option value="">Select User Type</option>
                                        @foreach($userTypes as $type => $label)
                                            @if(!in_array($type, $existingTypes))
                                                <option value="{{ $type }}" {{ old('user_type') == $type ? 'selected' : '' }}>
                                                    {{ $label }}
                                                </option>
                                            @endif
                                        @endforeach
                                    </select>
                                    @error('user_type')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Select the type of users who will receive this welcome message</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="subject" class="form-label">Email Subject <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('subject') is-invalid @enderror"
                                           id="subject" name="subject" value="{{ old('subject') }}" required>
                                    @error('subject')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="greeting" class="form-label">Greeting <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('greeting') is-invalid @enderror"
                                   id="greeting" name="greeting" value="{{ old('greeting') }}" required>
                            @error('greeting')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">The greeting line that appears at the top of the email (e.g., "Welcome to our platform!")</div>
                        </div>

                        <div class="mb-3">
                            <label for="content" class="form-label">Message Content <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('content') is-invalid @enderror"
                                      id="content" name="content" rows="8" required>{{ old('content') }}</textarea>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">The main content of the welcome email. Use line breaks to separate paragraphs.</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="call_to_action_text" class="form-label">Call to Action Text</label>
                                    <input type="text" class="form-control @error('call_to_action_text') is-invalid @enderror"
                                           id="call_to_action_text" name="call_to_action_text" value="{{ old('call_to_action_text') }}">
                                    @error('call_to_action_text')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Text for the main button (e.g., "Get Started", "View Dashboard")</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="call_to_action_url" class="form-label">Call to Action URL</label>
                                    <input type="url" class="form-control @error('call_to_action_url') is-invalid @enderror"
                                           id="call_to_action_url" name="call_to_action_url" value="{{ old('call_to_action_url') }}">
                                    @error('call_to_action_url')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">URL the button should link to (e.g., "/dashboard", "/affiliate/dashboard")</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1"
                                       {{ old('is_active', true) ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">
                                    Active Message
                                </label>
                            </div>
                            <div class="form-text">Only active messages will be sent to new users. Only one message per user type can be active.</div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('super_admin.welcome-messages.index') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Welcome Message
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle"></i> Tips for Writing Welcome Messages
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Content Guidelines:</h6>
                            <ul class="small">
                                <li>Keep the message warm and welcoming</li>
                                <li>Clearly explain what users can expect</li>
                                <li>Include next steps or getting started tips</li>
                                <li>Mention support availability</li>
                                <li>Use line breaks to separate paragraphs</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>User Type Specific Tips:</h6>
                            <ul class="small">
                                <li><strong>Organization:</strong> Focus on business benefits and setup steps</li>
                                <li><strong>Affiliate:</strong> Highlight earning potential and commission structure</li>
                                <li><strong>Super Admin:</strong> Emphasize responsibilities and security</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-populate fields based on user type selection
document.getElementById('user_type').addEventListener('change', function() {
    const userType = this.value;
    const defaults = {
        'organization': {
            subject: 'Welcome to {{ config("app.name", "Sales Management System") }}!',
            greeting: 'Welcome to our platform!',
            content: "Thank you for joining {{ config('app.name', 'Sales Management System') }}. We're excited to help you streamline your business operations and boost your productivity.\n\nTo get started:\n1. Complete your organization profile\n2. Set up your first branch\n3. Add team members\n4. Start managing your orders\n\nIf you need any assistance, our support team is here to help.",
            cta_text: 'Get Started',
            cta_url: '/dashboard'
        },
        'affiliate': {
            subject: 'Welcome to Our Affiliate Program!',
            greeting: 'Welcome to our affiliate family!',
            content: "Thank you for joining our affiliate program. Start earning commissions by referring new organizations to our platform.\n\nYour benefits:\n• Competitive commission rates\n• Real-time tracking\n• Monthly payouts\n• Dedicated support\n\nGet your unique referral link and start promoting today!",
            cta_text: 'View Dashboard',
            cta_url: '/affiliate/dashboard'
        },
        'super_admin': {
            subject: 'Super Admin Access Granted',
            greeting: 'Welcome, Administrator!',
            content: "Your super admin account has been created successfully. You now have full access to manage the {{ config('app.name', 'Sales Management System') }} platform.\n\nYour responsibilities include:\n• Managing organizations and users\n• Monitoring system performance\n• Handling support requests\n• Configuring system settings\n\nPlease ensure you keep your credentials secure.",
            cta_text: 'Access Admin Panel',
            cta_url: '/super-admin/dashboard'
        }
    };

    if (defaults[userType]) {
        const def = defaults[userType];
        if (!document.getElementById('subject').value) {
            document.getElementById('subject').value = def.subject;
        }
        if (!document.getElementById('greeting').value) {
            document.getElementById('greeting').value = def.greeting;
        }
        if (!document.getElementById('content').value) {
            document.getElementById('content').value = def.content;
        }
        if (!document.getElementById('call_to_action_text').value) {
            document.getElementById('call_to_action_text').value = def.cta_text;
        }
        if (!document.getElementById('call_to_action_url').value) {
            document.getElementById('call_to_action_url').value = def.cta_url;
        }
    }
});
</script>
@endsection
