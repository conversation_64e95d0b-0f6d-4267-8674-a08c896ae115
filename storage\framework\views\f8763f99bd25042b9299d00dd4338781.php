

<?php $__env->startSection('title', 'Organizations'); ?>
<?php $__env->startSection('page-title', 'Organizations'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Organizations</h1>
            <p class="text-muted">Manage all organizations in the system</p>
        </div>
        <a href="<?php echo e(route('super_admin.organizations.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Add Organization
        </a>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="<?php echo e(route('super_admin.organizations.index')); ?>" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search"
                           value="<?php echo e(request('search')); ?>" placeholder="Search organizations...">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">All Statuses</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="plan" class="form-label">Plan</label>
                    <select class="form-select" id="plan" name="plan">
                        <option value="">All Plans</option>
                        <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($plan->id); ?>" <?php echo e(request('plan') == $plan->id ? 'selected' : ''); ?>>
                                <?php echo e($plan->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="fas fa-search"></i> Filter
                    </button>
                    <a href="<?php echo e(route('super_admin.organizations.index')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i> Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Organizations Table -->
    <div class="card shadow">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Organizations (<?php echo e($organizations->total()); ?>)
            </h6>
        </div>
        <div class="card-body">
            <?php if($organizations->count() > 0): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Organization</th>
                                <th>Plan</th>
                                <th>Users</th>
                                <th>Branches</th>
                                <th>Subscriptions</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $organizations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $organization): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <?php if($organization->logo): ?>
                                                <img src="<?php echo e(asset('storage/logos/' . $organization->logo)); ?>"
                                                     alt="<?php echo e($organization->name); ?>"
                                                     class="rounded-circle me-3" width="40" height="40">
                                            <?php else: ?>
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3"
                                                     style="width: 40px; height: 40px;">
                                                    <span class="text-white font-weight-bold">
                                                        <?php echo e(substr($organization->name, 0, 1)); ?>

                                                    </span>
                                                </div>
                                            <?php endif; ?>
                                            <div>
                                                <div class="font-weight-bold"><?php echo e($organization->name); ?></div>
                                                <small class="text-muted"><?php echo e($organization->email); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if($organization->plan): ?>
                                            <span class="badge bg-info"><?php echo e($organization->plan->name); ?></span>
                                            <br><small class="text-muted"><?php echo e(format_price($organization->plan->price)); ?>/month</small>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">No Plan</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary"><?php echo e($organization->users_count); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success"><?php echo e($organization->branches_count); ?></span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning"><?php echo e($organization->subscriptions_count); ?></span>
                                    </td>
                                    <td>
                                        <?php if($organization->is_active): ?>
                                            <span class="badge bg-success">Active</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Inactive</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small><?php echo e($organization->created_at->format('M j, Y')); ?></small>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?php echo e(route('super_admin.organizations.show', $organization)); ?>"
                                               class="btn btn-sm btn-outline-primary" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('super_admin.organizations.edit', $organization)); ?>"
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if($organization->is_active): ?>
                                                <form method="POST" action="<?php echo e(route('super_admin.organizations.deactivate', $organization)); ?>"
                                                      class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-warning"
                                                            title="Deactivate" onclick="return confirm('Are you sure?')">
                                                        <i class="fas fa-pause"></i>
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <form method="POST" action="<?php echo e(route('super_admin.organizations.activate', $organization)); ?>"
                                                      class="d-inline">
                                                    <?php echo csrf_field(); ?>
                                                    <button type="submit" class="btn btn-sm btn-outline-success"
                                                            title="Activate">
                                                        <i class="fas fa-play"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        Showing <?php echo e($organizations->firstItem()); ?> to <?php echo e($organizations->lastItem()); ?>

                        of <?php echo e($organizations->total()); ?> results
                    </div>
                    <?php echo e($organizations->links()); ?>

                </div>
            <?php else: ?>
                <div class="text-center py-5">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No organizations found</h5>
                    <p class="text-muted">Get started by creating your first organization.</p>
                    <a href="<?php echo e(route('super_admin.organizations.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Organization
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('super_admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\SalesManagementSystem\resources\views/super_admin/organizations/index.blade.php ENDPATH**/ ?>