<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WelcomeMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_type',
        'subject',
        'greeting',
        'content',
        'call_to_action_text',
        'call_to_action_url',
        'is_active',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // User type constants
    const TYPE_ORGANIZATION = 'organization';
    const TYPE_AFFILIATE = 'affiliate';
    const TYPE_SUPER_ADMIN = 'super_admin';

    /**
     * Get the user who created this message.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this message.
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope for active messages.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific user type.
     */
    public function scopeForUserType($query, $userType)
    {
        return $query->where('user_type', $userType);
    }

    /**
     * Get the active welcome message for a specific user type.
     */
    public static function getActiveMessage($userType)
    {
        return static::active()
            ->forUserType($userType)
            ->first();
    }

    /**
     * Get available user types.
     */
    public static function getUserTypes()
    {
        return [
            self::TYPE_ORGANIZATION => 'Organization Users',
            self::TYPE_AFFILIATE => 'Affiliate Users',
            self::TYPE_SUPER_ADMIN => 'Super Admin Users',
        ];
    }

    /**
     * Get formatted user type.
     */
    public function getFormattedUserTypeAttribute()
    {
        $types = self::getUserTypes();
        return $types[$this->user_type] ?? ucfirst($this->user_type);
    }

    /**
     * Get default welcome message content.
     */
    public static function getDefaultContent($userType)
    {
        $defaults = [
            self::TYPE_ORGANIZATION => [
                'subject' => 'Welcome to ' . config('app.name', 'Sales Management System') . '!',
                'greeting' => 'Welcome to our platform!',
                'content' => "Thank you for joining " . config('app.name', 'Sales Management System') . ". We're excited to help you streamline your business operations and boost your productivity.\n\nTo get started:\n1. Complete your organization profile\n2. Set up your first branch\n3. Add team members\n4. Start managing your orders\n\nIf you need any assistance, our support team is here to help.",
                'call_to_action_text' => 'Get Started',
                'call_to_action_url' => '/dashboard',
            ],
            self::TYPE_AFFILIATE => [
                'subject' => 'Welcome to Our Affiliate Program!',
                'greeting' => 'Welcome to our affiliate family!',
                'content' => "Thank you for joining our affiliate program. Start earning commissions by referring new organizations to our platform.\n\nYour benefits:\n• Competitive commission rates\n• Real-time tracking\n• Monthly payouts\n• Dedicated support\n\nGet your unique referral link and start promoting today!",
                'call_to_action_text' => 'View Dashboard',
                'call_to_action_url' => '/affiliate/dashboard',
            ],
            self::TYPE_SUPER_ADMIN => [
                'subject' => 'Super Admin Access Granted',
                'greeting' => 'Welcome, Administrator!',
                'content' => "Your super admin account has been created successfully. You now have full access to manage the " . config('app.name', 'Sales Management System') . " platform.\n\nYour responsibilities include:\n• Managing organizations and users\n• Monitoring system performance\n• Handling support requests\n• Configuring system settings\n\nPlease ensure you keep your credentials secure.",
                'call_to_action_text' => 'Access Admin Panel',
                'call_to_action_url' => '/super-admin/dashboard',
            ],
        ];

        return $defaults[$userType] ?? $defaults[self::TYPE_ORGANIZATION];
    }

    /**
     * Create default welcome messages for all user types.
     */
    public static function createDefaults($createdBy = null)
    {
        foreach (self::getUserTypes() as $type => $label) {
            $existing = self::where('user_type', $type)->first();

            if (!$existing) {
                $defaults = self::getDefaultContent($type);
                self::create([
                    'user_type' => $type,
                    'subject' => $defaults['subject'],
                    'greeting' => $defaults['greeting'],
                    'content' => $defaults['content'],
                    'call_to_action_text' => $defaults['call_to_action_text'],
                    'call_to_action_url' => $defaults['call_to_action_url'],
                    'is_active' => true,
                    'created_by' => $createdBy,
                ]);
            }
        }
    }
}
