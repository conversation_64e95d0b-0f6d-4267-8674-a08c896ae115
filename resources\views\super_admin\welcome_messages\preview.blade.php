@extends('super_admin.layouts.app')

@section('title', 'Preview Welcome Message')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">Preview Welcome Message</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('super_admin.welcome-messages.index') }}">Welcome Messages</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('super_admin.welcome-messages.show', $welcomeMessage) }}">{{ $welcomeMessage->formatted_user_type }}</a>
                            </li>
                            <li class="breadcrumb-item active">Preview</li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group" role="group">
                    <a href="{{ route('super_admin.welcome-messages.edit', $welcomeMessage) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <a href="{{ route('super_admin.welcome-messages.show', $welcomeMessage) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Details
                    </a>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            @if(session('error') || $errors->any())
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ session('error') ?? $errors->first() }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <!-- Email Preview -->
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-envelope"></i> Email Preview
                            </h5>
                            <span class="badge bg-info">{{ $welcomeMessage->formatted_user_type }}</span>
                        </div>
                        <div class="card-body p-0">
                            <!-- Email Preview Container -->
                            <div style="background-color: #f8f9fa; padding: 20px;">
                                <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">

                                    <!-- Email Header -->
                                    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px 20px; text-align: center;">
                                        <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: 300;">Welcome!</h1>
                                        <p style="color: #e8eaff; margin: 5px 0 0 0; font-size: 14px;">Thank you for joining our platform</p>
                                    </div>

                                    <!-- Email Body -->
                                    <div style="padding: 40px 30px;">
                                        <div style="font-size: 18px; color: #555555; margin-bottom: 25px;">
                                            {{ $welcomeMessage->greeting }}
                                        </div>

                                        @foreach(explode("\n", $welcomeMessage->content) as $paragraph)
                                            @if(trim($paragraph))
                                                <p style="margin: 0 0 16px 0; font-size: 16px; line-height: 1.6; color: #333333;">
                                                    {{ trim($paragraph) }}
                                                </p>
                                            @endif
                                        @endforeach

                                        @if($welcomeMessage->call_to_action_text && $welcomeMessage->call_to_action_url)
                                            <div style="text-align: center; margin: 30px 0;">
                                                <a href="{{ url($welcomeMessage->call_to_action_url) }}"
                                                   style="display: inline-block; padding: 14px 28px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: #ffffff; text-decoration: none; border-radius: 6px; font-weight: 500; font-size: 16px;">
                                                    {{ $welcomeMessage->call_to_action_text }}
                                                </a>
                                            </div>
                                        @endif

                                        <div style="height: 1px; background-color: #e9ecef; margin: 30px 0;"></div>

                                        <div style="background-color: #f8f9ff; border-left: 4px solid #667eea; padding: 20px; margin: 25px 0; border-radius: 0 6px 6px 0;">
                                            <h3 style="margin: 0 0 10px 0; color: #333333; font-size: 18px;">Need Help?</h3>
                                            <p style="margin: 0; color: #666666; font-size: 16px;">
                                                If you have any questions or need assistance, don't hesitate to reach out to our support team.
                                                We're here to ensure you have the best experience possible.
                                            </p>
                                        </div>

                                        <p style="text-align: center; color: #6c757d; font-size: 14px; margin: 0;">
                                            This email was sent to {{ $dummyUser->email }} because you created an account with Sales Management System.
                                        </p>
                                    </div>

                                    <!-- Email Footer -->
                                    <div style="background-color: #f8f9fa; padding: 30px; text-align: center; border-top: 1px solid #e9ecef;">
                                        <p style="margin: 0 0 10px 0; font-size: 14px; color: #6c757d;"><strong>{{ config('app.name', 'Sales Management System') }}</strong></p>
                                        <p style="margin: 0 0 10px 0; font-size: 14px; color: #6c757d;">Professional Business Management Solution</p>

                                        <div style="height: 1px; background-color: #e9ecef; margin: 20px 0;"></div>

                                        <p style="margin: 0 0 10px 0; font-size: 12px; color: #6c757d;">
                                            This email was sent to you because you have an account with {{ config('app.name', 'Sales Management System') }}.
                                            If you have any questions, please contact our support team.
                                        </p>

                                        <p style="margin: 0; font-size: 12px; color: #6c757d;">
                                            © {{ date('Y') }} Sales Management System. All rights reserved.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preview Info & Actions -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i> Preview Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label text-muted">Subject Line</label>
                                <div class="fw-bold">{{ $welcomeMessage->subject }}</div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Recipient Type</label>
                                <div>
                                    <span class="badge bg-info">{{ $welcomeMessage->formatted_user_type }}</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <div>
                                    @if($welcomeMessage->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-secondary">Inactive</span>
                                    @endif
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label text-muted">Sample Recipient</label>
                                <div>{{ $dummyUser->name }} ({{ $dummyUser->email }})</div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Email -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-paper-plane"></i> Send Test Email
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ route('super_admin.welcome-messages.send-test', $welcomeMessage) }}">
                                @csrf
                                <div class="mb-3">
                                    <input type="email" class="form-control" name="test_email"
                                           placeholder="Enter email address" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-paper-plane"></i> Send Test Email
                                </button>
                            </form>
                            <div class="form-text mt-2">Send this email to yourself or a test address to see how it looks in an actual email client.</div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-cogs"></i> Actions
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('super_admin.welcome-messages.edit', $welcomeMessage) }}" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> Edit Message
                                </a>

                                <form method="POST" action="{{ route('super_admin.welcome-messages.toggle-status', $welcomeMessage) }}">
                                    @csrf
                                    <button type="submit" class="btn btn-{{ $welcomeMessage->is_active ? 'danger' : 'success' }} w-100"
                                            onclick="return confirm('{{ $welcomeMessage->is_active ? 'Deactivate' : 'Activate' }} this message?')">
                                        <i class="fas fa-{{ $welcomeMessage->is_active ? 'ban' : 'check' }}"></i>
                                        {{ $welcomeMessage->is_active ? 'Deactivate' : 'Activate' }}
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
