<?php

// Web-based database fix script
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Fix Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        .button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .button:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1><?php echo $_ENV['APP_NAME'] ?? 'Sales Management System'; ?> - Database Fix Tool</h1>

    <?php
    if (isset($_POST['fix_database'])) {
        echo "<h2>Fixing Database Issues...</h2>";

        // Database configuration
        $host = '127.0.0.1';
        $port = '4306';
        $database = 'ofp_pro';
        $username = 'root';
        $password = '';

        try {
            // Connect to database
            $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
            $pdo = new PDO($dsn, $username, $password, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            ]);

            echo "<p class='success'>✓ Connected to database: {$database}</p>";

            // Fix sessions table
            $stmt = $pdo->query("SHOW TABLES LIKE 'sessions'");
            if ($stmt->rowCount() == 0) {
                echo "<p class='info'>Creating sessions table...</p>";

                $createSessionsTable = "
                CREATE TABLE `sessions` (
                    `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                    `user_id` bigint unsigned DEFAULT NULL,
                    `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                    `user_agent` text COLLATE utf8mb4_unicode_ci,
                    `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
                    `last_activity` int NOT NULL,
                    PRIMARY KEY (`id`),
                    KEY `sessions_user_id_index` (`user_id`),
                    KEY `sessions_last_activity_index` (`last_activity`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";

                $pdo->exec($createSessionsTable);
                echo "<p class='success'>✓ Sessions table created</p>";
            } else {
                echo "<p class='success'>✓ Sessions table already exists</p>";
            }

            // Check announcements table
            $stmt = $pdo->query("SHOW TABLES LIKE 'announcements'");
            if ($stmt->rowCount() == 0) {
                echo "<p class='info'>Creating announcements table...</p>";

                $createAnnouncementsTable = "
                CREATE TABLE `announcements` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                    `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
                    `type` enum('info','warning','success','danger','maintenance') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'info',
                    `priority` enum('low','normal','high','urgent') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal',
                    `target_audience` enum('all','customers','organizations','admins') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'all',
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `is_dismissible` tinyint(1) NOT NULL DEFAULT '1',
                    `show_on_login` tinyint(1) NOT NULL DEFAULT '0',
                    `show_on_dashboard` tinyint(1) NOT NULL DEFAULT '1',
                    `send_email` tinyint(1) NOT NULL DEFAULT '0',
                    `affected_features` json DEFAULT NULL,
                    `starts_at` timestamp NULL DEFAULT NULL,
                    `ends_at` timestamp NULL DEFAULT NULL,
                    `published_at` timestamp NULL DEFAULT NULL,
                    `created_by` bigint unsigned NOT NULL,
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    KEY `announcements_is_active_published_at_index` (`is_active`,`published_at`),
                    KEY `announcements_type_target_audience_index` (`type`,`target_audience`),
                    KEY `announcements_starts_at_ends_at_index` (`starts_at`,`ends_at`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";

                $pdo->exec($createAnnouncementsTable);
                echo "<p class='success'>✓ Announcements table created</p>";
            } else {
                echo "<p class='success'>✓ Announcements table already exists</p>";
            }

            // Check user_announcement_interactions table
            $stmt = $pdo->query("SHOW TABLES LIKE 'user_announcement_interactions'");
            if ($stmt->rowCount() == 0) {
                echo "<p class='info'>Creating user_announcement_interactions table...</p>";

                $createInteractionsTable = "
                CREATE TABLE `user_announcement_interactions` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `user_id` bigint unsigned NOT NULL,
                    `announcement_id` bigint unsigned NOT NULL,
                    `is_read` tinyint(1) NOT NULL DEFAULT '0',
                    `is_dismissed` tinyint(1) NOT NULL DEFAULT '0',
                    `read_at` timestamp NULL DEFAULT NULL,
                    `dismissed_at` timestamp NULL DEFAULT NULL,
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `user_announcement_interactions_user_id_announcement_id_unique` (`user_id`,`announcement_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";

                $pdo->exec($createInteractionsTable);
                echo "<p class='success'>✓ User announcement interactions table created</p>";
            } else {
                echo "<p class='success'>✓ User announcement interactions table already exists</p>";
            }

            // Check super_admins table
            $stmt = $pdo->query("SHOW TABLES LIKE 'super_admins'");
            if ($stmt->rowCount() == 0) {
                echo "<p class='info'>Creating super_admins table...</p>";

                $createSuperAdminsTable = "
                CREATE TABLE `super_admins` (
                    `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                    `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                    `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                    `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                    `role` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'super_admin',
                    `is_active` tinyint(1) NOT NULL DEFAULT '1',
                    `last_login_at` timestamp NULL DEFAULT NULL,
                    `created_at` timestamp NULL DEFAULT NULL,
                    `updated_at` timestamp NULL DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    UNIQUE KEY `super_admins_email_unique` (`email`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
                ";

                $pdo->exec($createSuperAdminsTable);
                echo "<p class='success'>✓ Super admins table created</p>";
            } else {
                echo "<p class='success'>✓ Super admins table already exists</p>";
            }

            // Create default super admin if none exist
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM super_admins");
            $result = $stmt->fetch();

            if ($result['count'] == 0) {
                echo "<p class='info'>Creating default super admin...</p>";

                $stmt = $pdo->prepare("
                    INSERT INTO super_admins (name, email, password, role, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                ");

                $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
                $stmt->execute([
                    'Super Admin',
                    '<EMAIL>',
                    $hashedPassword,
                    'super_admin',
                    1
                ]);

                echo "<p class='success'>✓ Default super admin created</p>";
                echo "<p class='info'><strong>Login Details:</strong><br>";
                echo "Email: <EMAIL><br>";
                echo "Password: password</p>";
            } else {
                echo "<p class='success'>✓ Super admin(s) already exist</p>";
            }

            // Create test announcements
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM announcements");
            $result = $stmt->fetch();

            if ($result['count'] == 0) {
                echo "<p class='info'>Creating test announcements...</p>";

                // Get super admin ID
                $stmt = $pdo->query("SELECT id FROM super_admins LIMIT 1");
                $superAdmin = $stmt->fetch();
                $superAdminId = $superAdmin['id'];

                $stmt = $pdo->prepare("
                    INSERT INTO announcements
                    (title, content, type, priority, target_audience, is_active, is_dismissible, show_on_login, show_on_dashboard, send_email, published_at, created_by, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, NOW(), NOW())
                ");

                // Test announcement for organizations
                $stmt->execute([
                    'Welcome to the System!',
                    'This is a test announcement for organization users. The announcement system is now working correctly.',
                    'info',
                    'normal',
                    'organizations',
                    1, 1, 0, 1, 0,
                    $superAdminId
                ]);

                // Test announcement for all users
                $stmt->execute([
                    'System Maintenance Notice',
                    'Please be aware that we may perform system maintenance during off-peak hours. You will be notified in advance of any scheduled downtime.',
                    'warning',
                    'high',
                    'all',
                    1, 1, 0, 1, 0,
                    $superAdminId
                ]);

                echo "<p class='success'>✓ Test announcements created</p>";
            } else {
                echo "<p class='success'>✓ Announcements already exist (count: {$result['count']})</p>";
            }

            echo "<h3 class='success'>✅ Database Fix Complete!</h3>";
            echo "<p><strong>Next Steps:</strong></p>";
            echo "<ul>";
            echo "<li>Login to super admin at: <a href='/SalesManagementSystem/super-admin/login'>/super-admin/login</a></li>";
            echo "<li>Email: <EMAIL></li>";
            echo "<li>Password: password</li>";
            echo "<li>Manage announcements at: <a href='/SalesManagementSystem/super-admin/announcements'>/super-admin/announcements</a></li>";
            echo "<li>Test the announcement system by logging in as an organization user</li>";
            echo "</ul>";

        } catch (PDOException $e) {
            echo "<p class='error'>✗ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p>Please check your database connection settings in .env file</p>";
        } catch (Exception $e) {
            echo "<p class='error'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        ?>
        <p>This tool will fix the database issues preventing the announcement system from working.</p>
        <p><strong>What this tool will do:</strong></p>
        <ul>
            <li>Create the missing 'sessions' table</li>
            <li>Ensure announcement-related tables exist</li>
            <li>Create a default super admin account</li>
            <li>Create test announcements</li>
        </ul>

        <form method="post">
            <button type="submit" name="fix_database" class="button">Fix Database Issues</button>
        </form>
        <?php
    }
    ?>
</body>
</html>
