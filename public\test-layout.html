<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout Test - Sidebar and Content</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /* Main layout container */
        .main-layout {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar styling */
        .sidebar {
            background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
            width: 256px;
            flex-shrink: 0;
            overflow-y: auto;
        }

        /* Main content area */
        .main-content {
            flex: 1;
            min-width: 0;
            overflow-x: hidden;
            background-color: #f8f9fa;
        }

        .sidebar .nav-link {
            color: #ecf0f1;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.125rem 0;
            transition: all 0.2s ease-in-out;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .sidebar .nav-link.active {
            background-color: #e74c3c;
            color: #fff;
        }

        /* Main content area adjustments */
        main.col-md-9 {
            padding-top: 0 !important;
            margin-top: 0 !important;
        }

        /* Ensure proper layout flow */
        .container-fluid {
            padding: 0;
        }

        .container-fluid .row {
            margin: 0;
        }

        /* Fix any potential overflow issues */
        body {
            overflow-x: hidden;
            background-color: #f8f9fa;
        }

        /* Mobile sidebar behavior */
        @media (max-width: 767.98px) {
            .main-layout {
                flex-direction: column;
            }

            .sidebar {
                display: none !important;
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 1050;
                width: 256px;
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
            }

            .sidebar.show {
                display: block !important;
                transform: translateX(0);
            }

            .main-content {
                width: 100%;
            }
        }

        .test-content {
            background: white;
            border-radius: 0.5rem;
            padding: 2rem;
            margin-bottom: 1rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
    </style>
</head>
<body>
    <!-- Mobile backdrop -->
    <div class="sidebar-backdrop d-md-none" id="sidebar-backdrop" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1040;"></div>

    <div class="main-layout">
        <!-- Sidebar -->
        <nav class="sidebar d-md-block" id="sidebar">
                <div class="pt-3 h-100">
                    <div class="sidebar-content p-3 h-100 d-flex flex-column">
                        <!-- Logo -->
                        <div class="text-center mb-4">
                            <h4 class="text-white">Test Organization</h4>
                            <small class="text-light">Sederly</small>
                        </div>

                        <!-- Navigation -->
                        <div class="flex-grow-1 overflow-auto">
                            <ul class="nav flex-column">
                                <li class="nav-item">
                                    <a href="#" class="nav-link active">
                                        <i class="fas fa-tachometer-alt me-2"></i>
                                        Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <i class="fas fa-shopping-cart me-2"></i>
                                        Orders
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <i class="fas fa-users me-2"></i>
                                        Customers
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        Reports
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <i class="fas fa-cog me-2"></i>
                                        Settings
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <i class="fas fa-user-friends me-2"></i>
                                        Users
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <i class="fas fa-building me-2"></i>
                                        Branches
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="#" class="nav-link">
                                        <i class="fas fa-credit-card me-2"></i>
                                        Billing
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

        <!-- Main content -->
        <main class="main-content">
                <!-- Top Navigation -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom" style="padding-left: 1.5rem; padding-right: 1.5rem;">
                    <div class="d-flex align-items-center">
                        <button class="btn btn-outline-secondary d-md-none me-2" type="button" id="mobile-menu-btn">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="h2 mb-0">Dashboard</h1>
                    </div>

                    <div class="d-flex align-items-center flex-wrap gap-2">
                        <!-- User Dropdown -->
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle d-flex align-items-center"
                                    type="button" id="userDropdown" data-bs-toggle="dropdown"
                                    aria-expanded="false" style="min-width: 120px;">
                                <i class="fas fa-user me-1"></i>
                                <span>Test User</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end shadow" aria-labelledby="userDropdown">
                                <li>
                                    <h6 class="dropdown-header">
                                        <i class="fas fa-user-circle me-2"></i>
                                        Test User
                                    </h6>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="#">
                                        <i class="fas fa-user-cog me-2"></i>Profile Settings
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <button type="button" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="px-4 py-3">
                    <div class="test-content">
                        <h3>Layout Test Results</h3>
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Testing Layout Issues</h5>
                            <p>This page tests the sidebar and main content layout to ensure:</p>
                            <ul>
                                <li>Sidebar doesn't push content down</li>
                                <li>Main content is visible without scrolling</li>
                                <li>Proper spacing and alignment</li>
                                <li>Responsive behavior on mobile</li>
                            </ul>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Dashboard Content</h5>
                                    </div>
                                    <div class="card-body">
                                        <p>This content should be visible immediately without scrolling down.</p>
                                        <p>The sidebar should be on the left, and this content should start at the top of the viewport.</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>Layout Check</h5>
                                    </div>
                                    <div class="card-body">
                                        <p><strong>✅ Expected:</strong> Content visible at top</p>
                                        <p><strong>❌ Problem:</strong> Need to scroll to see content</p>
                                        <p><strong>🔧 Fixed:</strong> Adjusted sidebar height and positioning</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mobile menu functionality
            const sidebar = document.getElementById('sidebar');
            const backdrop = document.getElementById('sidebar-backdrop');
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');

            if (sidebar && backdrop && mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    if (window.innerWidth < 768) {
                        const isVisible = sidebar.classList.contains('show');
                        if (isVisible) {
                            sidebar.classList.remove('show');
                            backdrop.style.display = 'none';
                        } else {
                            sidebar.classList.add('show');
                            backdrop.style.display = 'block';
                        }
                    }
                });

                backdrop.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                    backdrop.style.display = 'none';
                });
            }
        });
    </script>
</body>
</html>
