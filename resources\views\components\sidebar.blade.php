@props(['settings', 'menuItems'])

<div class="pt-3 h-100" style="overflow-y: auto;">
    <div class="sidebar-content p-3 h-100 d-flex flex-column">
        <!-- Logo -->
        <div class="text-center mb-4">
            <h4 class="text-white">{{ $settings->organization_name ?? 'Organization' }}</h4>
            <small class="text-light">{{ $settings->app_name ?? config('app.name', 'Sales Management System') }}</small>
        </div>



        <!-- Navigation (Scrollable) -->
        <div class="flex-grow-1 overflow-auto">
            <ul class="nav flex-column">
                @if(isset($menuItems) && count($menuItems) > 0)
                    @foreach($menuItems as $item)
                        @if(isset($item['route']))
                        <li class="nav-item">
                            <a href="{{ route($item['route']) }}"
                               @if(isset($item['onclick'])) onclick="{{ $item['onclick'] }}" @endif
                               class="nav-link {{ $item['active'] ? 'active' : '' }}">
                                @if(isset($item['icon']))
                                    <i class="fas fa-{{ $item['icon'] }} me-2"></i>
                                @endif
                                {{ $item['label'] }}
                                @if(isset($item['badge']))
                                    <span class="badge rounded-pill {{ $item['badge_color'] ?? 'bg-secondary' }} ms-2">
                                        {{ $item['badge'] }}
                                    </span>
                                @endif
                            </a>
                        </li>
                        @endif
                    @endforeach
                @else
                    <!-- Fallback menu items if none are generated -->
                    <li class="nav-item">
                        <a href="{{ route('dashboard') }}" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            Dashboard
                        </a>
                    </li>
                    @auth
                        @if(auth()->user()->hasRole('Staff'))
                        <li class="nav-item">
                            <a href="{{ route('orders.create') }}" class="nav-link {{ request()->routeIs('orders.create') ? 'active' : '' }}">
                                <i class="fas fa-plus-circle me-2"></i>
                                Create Order
                            </a>
                        </li>
                        @endif
                        <li class="nav-item">
                            <a href="{{ route('orders.index') }}" class="nav-link {{ request()->routeIs('orders.*') ? 'active' : '' }}">
                                <i class="fas fa-shopping-cart me-2"></i>
                                View Orders
                            </a>
                        </li>
                        @if(auth()->user()->hasAnyRole(['Organization Owner', 'Manager']))
                        <li class="nav-item">
                            <a href="{{ route('users.index') }}" class="nav-link {{ request()->routeIs('users.*') ? 'active' : '' }}">
                                <i class="fas fa-users me-2"></i>
                                User Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('settings.index') }}" class="nav-link {{ request()->routeIs('settings.*') ? 'active' : '' }}">
                                <i class="fas fa-cog me-2"></i>
                                Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('billing.index') }}" class="nav-link {{ request()->routeIs('billing.*') ? 'active' : '' }}">
                                <i class="fas fa-credit-card me-2"></i>
                                Billing & Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('plan-change.index') }}" class="nav-link {{ request()->routeIs('plan-change.*') ? 'active' : '' }}">
                                <i class="fas fa-layer-group me-2"></i>
                                Subscription Plans
                            </a>
                        </li>
                        @endif
                        @if(!auth()->user()->hasRole(['Staff', 'Production', 'Delivery']))
                        <li class="nav-item">
                            <a href="{{ route('expenditures.index') }}" class="nav-link {{ request()->routeIs('expenditures.*') ? 'active' : '' }}">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                Expenditures
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('financial.overview') }}" class="nav-link {{ request()->routeIs('financial.*') ? 'active' : '' }}">
                                <i class="fas fa-chart-bar me-2"></i>
                                Financial Overview
                            </a>
                        </li>
                        @endif
                        <li class="nav-item">
                            <a href="{{ route('user.support.index') }}" class="nav-link {{ request()->routeIs('user.support.*') ? 'active' : '' }}">
                                <i class="fas fa-headset me-2"></i>
                                Support Center
                            </a>
                        </li>
                    @endauth
                @endif
            </ul>
        </div>

        <!-- Footer -->
        <div class="mt-auto pt-3 border-top border-secondary">
            <div class="text-center">
                <small class="text-muted d-block">OderFlow Pro v4.0.1</small>
                <small class="text-muted">Kuronicz Tech</small>
            </div>
        </div>

    </div>
</div>

@once
@push('scripts')
<script>
function initiateBackup(event) {
    event.preventDefault();

    Swal.fire({
        title: 'Create Database Backup',
        text: 'Are you sure you want to create a database backup?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Yes, create backup',
        cancelButtonText: 'Cancel',
        showLoaderOnConfirm: true,
        preConfirm: () => {
            return fetch('/database/backup', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    throw new Error(data.message || 'Backup failed');
                }
                return data;
            })
            .catch(error => {
                Swal.showValidationMessage(`Request failed: ${error.message}`);
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'Success!',
                text: result.value.message,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        }
    });
}
</script>
@endpush
@endonce

<style>
    /* Custom Scrollbar Styles */
    .scrollbar-thin::-webkit-scrollbar {
        width: 2px;
    }

    .scrollbar-thin::-webkit-scrollbar-track {
        background-color: rgba(31, 41, 55, 0.8);
    }

    .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(156, 163, 175, 0.3);
        border-radius: 1px;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb:hover {
        background-color: rgba(156, 163, 175, 0.5);
    }

    /* Firefox */
    .scrollbar-thin {
        scrollbar-width: thin;
        scrollbar-color: rgba(156, 163, 175, 0.3) rgba(31, 41, 55, 0.8);
    }
</style>
